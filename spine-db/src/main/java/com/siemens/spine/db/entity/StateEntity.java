package com.siemens.spine.db.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.sql.Timestamp;

@Entity
@Table(name = "state",
        indexes = { @Index(name = "idx_state_component", columnList = "component_id"),
                @Index(name = "idx_state_changegroup", columnList = "change_group_id") })
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Audited
public class StateEntity implements SpineEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "state_id_seq_gen")
    @SequenceGenerator(name = "state_id_seq_gen", sequenceName = "state_id_seq")
    @Column(name = "id")
    private Long id;

    @Column(name = "state")
    private Integer state;

    @Column(name = "reason")
    private Integer reason;

    @Column(name = "username")
    private String username;

    @Column(name = "remark")
    private String remark;

    @Column(name = "status_date")
    @CreationTimestamp
    private Timestamp statusDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "component_id")
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    private ComponentEntity component;

    @ManyToOne
    @JoinColumn(name = "change_group_id")
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    private ChangeGroupEntity changeGroup;

}

