package com.siemens.spine.db.entity.elcon;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

/**
 * Test entity demonstrating composite key implementation with multiple @Id fields.
 * This entity uses system-generated IDs and flat JSON structure as per requirements.
 * 
 * URL format: {customerId}-{productId}
 * JSON format: {"customerId": 123, "productId": 456, "description": "Test"}
 *
 * <AUTHOR> Agent
 * @since 1.0
 */
@Entity
@Table(name = "e_composite_key_test")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ECompositeKeyTest {

    /**
     * First component of composite key - system generated customer ID.
     */
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "customer_seq")
    @SequenceGenerator(name = "customer_seq", sequenceName = "customer_id_seq", allocationSize = 1)
    @Column(name = "customer_id", nullable = false)
    @JsonProperty("customerId")
    private Long customerId;

    /**
     * Second component of composite key - system generated product ID.
     */
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "product_seq")
    @SequenceGenerator(name = "product_seq", sequenceName = "product_id_seq", allocationSize = 1)
    @Column(name = "product_id", nullable = false)
    @JsonProperty("productId")
    private Long productId;

    /**
     * Regular entity field - not part of the composite key.
     */
    @Column(name = "description")
    @JsonProperty("description")
    private String description;

    /**
     * Regular entity field - not part of the composite key.
     */
    @Column(name = "quantity")
    @JsonProperty("quantity")
    private Integer quantity;

    /**
     * Regular entity field - not part of the composite key.
     */
    @Column(name = "price")
    @JsonProperty("price")
    private Double price;
}
