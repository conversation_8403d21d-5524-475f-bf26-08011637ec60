package com.siemens.spine.logic.util;

import com.siemens.spine.logic.exception.SpineException;
import lombok.extern.slf4j.Slf4j;

import javax.persistence.Id;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Utility service for entity reflection operations.
 * Provides methods for ID extraction, setting, and type conversion
 * commonly needed when working with JPA entities in REST operations.
 *
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
public final class EntityReflectionUtils {

    private EntityReflectionUtils() {
    }

    /**
     * Converts a string ID to the appropriate type based on the target class.
     *
     * @param idStr   The string representation of the ID
     * @param idClass The target class for the ID
     * @return The converted ID object
     * @throws SpineException if conversion fails
     */
    public static Object convertId(String idStr, Class<?> idClass) throws SpineException {
        Objects.requireNonNull(idStr, "ID string cannot be null");
        Objects.requireNonNull(idClass, "ID class cannot be null");

        try {
            if (idClass == Long.class || idClass == long.class) {
                return Long.parseLong(idStr);
            } else if (idClass == Integer.class || idClass == int.class) {
                return Integer.parseInt(idStr);
            } else if (idClass == String.class) {
                return idStr;
            } else {
                // current does not support composite key
                log.warn("Unsupported ID type: {}. Returning as string.", idClass.getSimpleName());
                return idStr;
            }
        } catch (NumberFormatException e) {
            log.error("Failed to convert ID '{}' to type {}", idStr, idClass.getSimpleName(), e);
            throw new SpineException("Failed to convert ID '" + idStr + "' to type " + idClass.getSimpleName(), e);
        }
    }

    /**
     * Extracts the ID value from an entity using reflection.
     * Looks for fields annotated with @Id.
     *
     * @param entity The entity from which to extract the ID
     * @return The ID value, or null if not found
     * @throws SpineException if reflection operations fail
     */
    public static Object extractId(Object entity) throws SpineException {
        Objects.requireNonNull(entity, "Entity cannot be null");

        Class<?> clazz = entity.getClass();
        Field idField = findIdField(clazz);

        if (idField == null) {
            log.error("No @Id annotated field found in entity class: {}", clazz.getSimpleName());
            throw new SpineException("No @Id annotated field found in entity class: " + clazz.getSimpleName());
        }
        return getFieldValue(entity, idField);
    }

    /**
     * Sets the ID value on an entity using reflection.
     * Looks for fields annotated with @Id.
     *
     * @param entity The entity on which to set the ID
     * @param id     The ID value to set
     * @throws SpineException if reflection operations fail
     */
    public static void setIdOnEntity(Object entity, Object id) throws SpineException {
        Objects.requireNonNull(entity, "Entity cannot be null");
        Objects.requireNonNull(id, "ID cannot be null");

        Class<?> clazz = entity.getClass();
        Field idField = findIdField(clazz);

        if (idField == null) {
            throw new SpineException("No @Id annotated field found in entity class: " + clazz.getSimpleName());
        }

        setFieldValue(entity, idField, id);
    }

    /**
     * Finds the field annotated with @Id in the given class.
     * Searches through the class hierarchy if necessary.
     *
     * @param clazz The class to search
     * @return The @Id annotated field, or null if not found
     */
    private static Field findIdField(Class<?> clazz) {
        // Search in current class
        for (Field field : clazz.getDeclaredFields()) {
            if (field.isAnnotationPresent(Id.class)) {
                return field;
            }
        }

        // Search in superclass if not found
        Class<?> superClass = clazz.getSuperclass();
        if (superClass != null && superClass != Object.class) {
            return findIdField(superClass);
        }

        return null;
    }

    /**
     * Gets the value of a field from an entity using reflection.
     *
     * @param entity The entity object
     * @param field  The field to read
     * @return The field value
     * @throws SpineException if reflection operations fail
     */
    @SuppressWarnings("all")
    private static Object getFieldValue(Object entity, Field field) throws SpineException {
        try {
            field.setAccessible(true);
            return field.get(entity);
        } catch (IllegalAccessException e) {
            throw new SpineException("Failed to access field '" + field.getName() + "' in entity class: " +
                    entity.getClass().getSimpleName(), e);
        } catch (SecurityException e) {
            throw new SpineException("Security exception accessing field '" + field.getName() + "' in entity class: " +
                    entity.getClass().getSimpleName(), e);
        }
    }

    /**
     * Sets the value of a field on an entity using reflection.
     *
     * @param entity The entity object
     * @param field  The field to set
     * @param value  The value to set
     * @throws SpineException if reflection operations fail
     */
    @SuppressWarnings("all")
    private static void setFieldValue(Object entity, Field field, Object value) throws SpineException {
        try {
            field.setAccessible(true);
            field.set(entity, value);
        } catch (IllegalAccessException e) {
            throw new SpineException("Failed to set field '" + field.getName() + "' in entity class: " +
                    entity.getClass().getSimpleName(), e);
        } catch (SecurityException e) {
            throw new SpineException("Security exception setting field '" + field.getName() + "' in entity class: " +
                    entity.getClass().getSimpleName(), e);
        } catch (IllegalArgumentException e) {
            throw new SpineException("Invalid argument setting field '" + field.getName() + "' with value type: " +
                    (value != null ? value.getClass().getSimpleName() : "null"), e);
        }
    }

}
