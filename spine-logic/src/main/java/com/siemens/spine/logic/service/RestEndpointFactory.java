package com.siemens.spine.logic.service;

import com.siemens.spine.db.repository.elcon.RestCrudRepositoryMarker;
import com.siemens.spine.db.repository.elcon.RestRepository;
import com.siemens.spine.logic.util.EntityReflectionUtils;
import com.siemens.spine.logic.util.ReflectionUtils;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.PostConstruct;
import javax.enterprise.context.ApplicationScoped;
import javax.enterprise.inject.Any;
import javax.enterprise.inject.Instance;
import javax.inject.Inject;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.StreamSupport;

/**
 * Service responsible for discovering and managing REST endpoints based on repository annotations.
 * This class scans for repositories marked with {@link RestRepository} annotation and generates
 * endpoint information for automatic REST API exposure.
 *
 * <p>Thread-safe implementation that caches discovered endpoints for performance.</p>
 *
 * @since 1.0
 */
@ApplicationScoped
@Slf4j
public class RestEndpointFactory {

    private static final String TARGET_PACKAGE = "com.siemens.spine.db.repository.elcon";

    // CDI instance of all repository beans
    private final Instance<RestCrudRepositoryMarker> repositoryBeans;

    // Thread-safe cache for generated endpoints
    private final ConcurrentMap<String, RestEndpointInfo> endpointCache = new ConcurrentHashMap<>();

    private volatile boolean initialized = false;

    @Inject
    public RestEndpointFactory(@Any Instance<RestCrudRepositoryMarker> repositoryBeans) {
        this.repositoryBeans = Objects.requireNonNull(repositoryBeans, "Repository beans cannot be null");
    }

    @PostConstruct
    public void initialize() {
        try {
            discoverAndCacheEndpoints();
            initialized = true;
            log.info("Successfully initialized RestEndpointGenerator with {} endpoints", endpointCache.size());
        } catch (Exception e) {
            log.error("Failed to initialize RestEndpointGenerator", e);
            throw new IllegalStateException("RestEndpointGenerator initialization failed", e);
        }
    }

    /**
     * Discovers and caches all REST endpoints from annotated repositories.
     * This method is called during initialization and should not be called directly.
     */
    private void discoverAndCacheEndpoints() {
        log.info("Starting endpoint discovery process");
        List<RestEndpointInfo> discoveredEndpoints = StreamSupport.stream(repositoryBeans.spliterator(), false)
                .map(this::processRepositoryBean)
                .filter(Objects::nonNull)
                .toList();

        discoveredEndpoints.forEach(endpoint ->
                endpointCache.put(endpoint.path(), endpoint));

        log.info("Discovered {} REST endpoints", discoveredEndpoints.size());
    }

    /**
     * Processes a single repository bean to extract endpoint information.
     *
     * @param repositoryBean the repository bean to process
     * @return RestEndpointInfo if valid endpoint found, null otherwise
     */
    private RestEndpointInfo processRepositoryBean(Object repositoryBean) {
        try {
            Class<?> actualClass = ReflectionUtils.resolveActualClass(repositoryBean);

            if (!ReflectionUtils.isInPackage(actualClass, TARGET_PACKAGE)) {
                return null;
            }

            return ReflectionUtils.getAnnotation(actualClass, RestRepository.class)
                    .map(annotation -> createEndpointInfo(repositoryBean, annotation, actualClass))
                    .orElse(null);

        } catch (Exception e) {
            log.error("Failed to process repository bean: {}", repositoryBean.getClass().getName(), e);
            return null;
        }
    }

    /**
     * Creates endpoint information from repository bean and annotation.
     *
     * @param repository      the repository instance
     * @param annotation      the RestRepository annotation
     * @param repositoryClass the repository class
     * @return RestEndpointInfo instance
     */
    private RestEndpointInfo createEndpointInfo(Object repository,
                                                RestRepository annotation,
                                                Class<?> repositoryClass) {
        Optional<Class<?>> entityClass = ReflectionUtils.extractEntityType(repositoryClass);
        Optional<Class<?>> idClass = ReflectionUtils.extractIdType(repositoryClass);

        if (entityClass.isEmpty()) {
            log.warn("Could not determine entity type for repository: {}", repositoryClass.getName());
            return null;
        }

        String path = determineEndpointPath(annotation, entityClass.get());
        boolean hasCompositeKey = EntityReflectionUtils.hasCompositeKey(entityClass.get());

        return new RestEndpointInfo(
                repository,
                entityClass.get(),
                idClass.orElse(null),
                path,
                hasCompositeKey
        );
    }

    /**
     * Determines the REST endpoint path based on annotation and entity class.
     *
     * @param annotation  the RestRepository annotation
     * @param entityClass the entity class
     * @return the endpoint path
     */
    private String determineEndpointPath(RestRepository annotation, Class<?> entityClass) {
        if (annotation.path().isEmpty()) {
            String defaultPath = entityClass.getSimpleName().toLowerCase() + "s";
            return ensureLeadingSlash(defaultPath);
        } else {
            return ensureLeadingSlash(annotation.path());
        }

    }

    /**
     * Ensures the path starts with a forward slash.
     *
     * @param path the path to check
     * @return path with leading slash
     */
    private String ensureLeadingSlash(String path) {
        return path.startsWith("/") ? path : "/" + path;
    }

    /**
     * Returns all discovered REST endpoints.
     * This method is thread-safe and returns a defensive copy of the endpoints.
     *
     * @return immutable list of REST endpoint information
     * @throws IllegalStateException if the generator is not properly initialized
     */
    public List<RestEndpointInfo> getGeneratedEndpoints() {
        ensureInitialized();
        return List.copyOf(endpointCache.values());
    }

    /**
     * Checks if the generator has been properly initialized.
     *
     * @throws IllegalStateException if not initialized
     */
    private void ensureInitialized() {
        if (!initialized) {
            throw new IllegalStateException("RestEndpointGenerator is not properly initialized");
        }
    }

    /**
     * Immutable record representing REST endpoint information.
     * Contains all necessary information to expose a repository as a REST endpoint.
     *
     * @param repository  The repository instance that handles the data operations
     * @param entityClass The entity class that this endpoint manages
     * @param idClass     The ID class for the entity (may be null if not determinable)
     * @param path        The REST endpoint path (always starts with '/')
     * @throws IllegalArgumentException if repository, entityClass, or path is null, path is empty or doesn't start with '/'
     */
    public record RestEndpointInfo(
            Object repository,
            Class<?> entityClass,
            Class<?> idClass,
            String path,
            boolean hasCompositeKey,
            CompositeKeyType keyType) {

        public RestEndpointInfo {
            Objects.requireNonNull(repository, "Repository cannot be null");
            Objects.requireNonNull(entityClass, "Entity class cannot be null");
            Objects.requireNonNull(path, "Path cannot be null");

            if (path.trim().isEmpty()) {
                throw new IllegalArgumentException("Path cannot be empty");
            }

            if (!path.startsWith("/")) {
                throw new IllegalArgumentException("Path must start with '/'");
            }
        }

    }

}