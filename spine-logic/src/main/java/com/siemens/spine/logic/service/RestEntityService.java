package com.siemens.spine.logic.service;

import com.siemens.spine.db.repository.CrudRepository;
import com.siemens.spine.db.repository.paging.Page;
import com.siemens.spine.db.repository.paging.Pageable;
import com.siemens.spine.logic.exception.SpineException;
import com.siemens.spine.logic.util.EntityReflectionUtils;
import com.siemens.spine.logic.util.JsonSerializationService;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import java.util.Objects;
import java.util.Optional;

/**
 * Service layer for handling REST entity operations.
 * This service encapsulates the business logic for CRUD operations on entities
 * exposed through REST endpoints, providing a clean separation between the
 * resource layer and repository layer.
 *
 * <AUTHOR> <PERSON>am
 * @since 1.0
 */
@ApplicationScoped
public class RestEntityService {

    private final RestEndpointFactory endpointGenerator;
    
    private final JsonSerializationService jsonSerializationService;

    /**
     * Constructor for dependency injection.
     *
     * @param endpointGenerator        Service for managing REST endpoints
     * @param jsonSerializationService Service for JSON serialization/deserialization
     */
    @Inject
    public RestEntityService(RestEndpointFactory endpointGenerator,
                             JsonSerializationService jsonSerializationService) {
        this.endpointGenerator = Objects.requireNonNull(endpointGenerator, "EndpointGenerator cannot be null");
        this.jsonSerializationService = Objects.requireNonNull(jsonSerializationService,
                "JsonSerializationService cannot be null");
    }

    /**
     * Retrieves all entities for the specified entity path.
     *
     * @param entityPath The entity path identifier
     * @return List of all entities
     * @throws SpineException if the entity path is not found or operation fails
     */
    public Page<Object> getPaginatedEntities(String entityPath, Pageable pageable) throws SpineException {
        RestEndpointFactory.RestEndpointInfo endpoint = getEndpointInfo(entityPath);
        CrudRepository<Object, Object> repository = getRepository(endpoint);
        return repository.findAll(pageable);
    }

    /**
     * Retrieves a single entity by its ID.
     *
     * @param entityPath The entity path identifier
     * @param idStr      The string representation of the entity ID
     * @return Optional containing the entity if found
     * @throws SpineException if the entity path is not found or ID conversion fails
     */
    public Optional<Object> getEntityById(String entityPath, String idStr) throws SpineException {
        RestEndpointFactory.RestEndpointInfo endpoint = getEndpointInfo(entityPath);

        if (endpoint.hasCompositeKey()) {
            return findByCompositeKey(endpoint.repository(), idStr, endpoint.entityClass());
        }

        Object id = getId(idStr, endpoint);
        CrudRepository<Object, Object> repository = getRepository(endpoint);
        return repository.findById(id);
    }

    /**
     * Finds entity by composite key URL segment.
     *
     * @param repository  The repository instance
     * @param urlSegment  The URL segment like "{123}-{PREMIUM}"
     * @param entityClass The entity class
     * @return Optional containing the entity if found
     * @throws SpineException if the operation fails
     */
    private Optional<Object> findByCompositeKey(Object repository, String urlSegment, Class<?> entityClass) throws SpineException {
        Map<String, Object> keyMap = EntityReflectionUtils.createCompositeKeyFromUrl(urlSegment, entityClass);
        // For now, we'll use a simple approach - this would need to be enhanced with actual composite key repository support
        log.warn("Composite key lookup not yet fully implemented for URL: {}", urlSegment);
        return Optional.empty();
    }

    /**
     * Creates a new entity from JSON data.
     *
     * @param entityPath The entity path identifier
     * @param jsonBody   The JSON representation of the entity
     * @return The created entity with generated ID
     * @throws SpineException if creation fails or JSON is invalid
     */
    public Object createEntity(String entityPath, String jsonBody) throws SpineException {
        RestEndpointFactory.RestEndpointInfo endpoint = getEndpointInfo(entityPath);
        Object entity = jsonSerializationService.deserializeEntity(jsonBody, endpoint.entityClass());
        CrudRepository<Object, Object> repository = getRepository(endpoint);
        return repository.save(entity);
    }

    /**
     * Updates an existing entity with new data from JSON.
     *
     * @param entityPath The entity path identifier
     * @param idStr      The string representation of the entity ID
     * @param jsonBody   The JSON representation of the updated entity
     * @return The updated entity
     * @throws SpineException if update fails, entity not found, or JSON is invalid
     */
    public Object updateEntity(String entityPath, String idStr, String jsonBody) throws SpineException {
        RestEndpointFactory.RestEndpointInfo endpoint = getEndpointInfo(entityPath);
        Object id = getId(idStr, endpoint);
        CrudRepository<Object, Object> repository = getRepository(endpoint);

        if (!repository.existsById(id)) {
            throw new SpineException("Entity not found with ID: " + idStr);
        }

        Object entity = jsonSerializationService.deserializeEntity(jsonBody, endpoint.entityClass());
        EntityReflectionUtils.setIdOnEntity(entity, id);

        return repository.save(entity);
    }

    /**
     * Deletes an entity by its ID.
     *
     * @param entityPath The entity path identifier
     * @param idStr      The string representation of the entity ID
     * @throws SpineException if deletion fails or entity not found
     */
    public void deleteEntity(String entityPath, String idStr) throws SpineException {
        RestEndpointFactory.RestEndpointInfo endpoint = getEndpointInfo(entityPath);
        Object id = getId(idStr, endpoint);
        CrudRepository<Object, Object> repository = getRepository(endpoint);

        if (!repository.existsById(id)) {
            throw new SpineException("Entity not found with ID: " + idStr);
        }

        repository.deleteById(id);
    }

    private Object getId(String idStr, RestEndpointFactory.RestEndpointInfo endpoint) throws SpineException {
        return EntityReflectionUtils.convertId(idStr, endpoint.idClass());
    }

    /**
     * Extracts the ID from a saved entity for location header generation.
     *
     * @param entity The entity from which to extract the ID
     * @return The extracted ID
     * @throws SpineException if ID extraction fails
     */
    public Object extractEntityId(Object entity) throws SpineException {
        return EntityReflectionUtils.extractId(entity);
    }

    /**
     * Retrieves endpoint information for the given entity path.
     *
     * @param entityPath The entity path identifier
     * @return The endpoint information
     * @throws SpineException if the endpoint is not found
     */
    private RestEndpointFactory.RestEndpointInfo getEndpointInfo(String entityPath) throws SpineException {
        return endpointGenerator.getGeneratedEndpoints().stream()
                .filter(info -> info.path().equals("/" + entityPath))
                .findFirst()
                .orElseThrow(() -> new SpineException("Entity path not found: " + entityPath));
    }

    /**
     * Safely casts the repository from the endpoint info.
     *
     * @param endpoint The endpoint information
     * @return The typed repository
     */
    @SuppressWarnings("unchecked")
    private CrudRepository<Object, Object> getRepository(RestEndpointFactory.RestEndpointInfo endpoint) {
        return (CrudRepository<Object, Object>) endpoint.repository();
    }

}
