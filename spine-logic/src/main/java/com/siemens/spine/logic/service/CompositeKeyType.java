package com.siemens.spine.logic.service;

/**
 * Enumeration representing different types of composite key implementations.
 * Used to categorize how composite keys are structured and handled.
 *
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 2/7/2025
 */
public enum CompositeKeyType {
    /**
     * Single ID field - not a composite key.
     */
    SINGLE,

    /**
     * Multiple @Id annotated fields (our implementation).
     */
    MULTIPLE_ID_FIELDS,

    /**
     * @EmbeddedId annotation (not currently supported).
     */
    EMBEDDED_ID,

    /**
     * @IdClass annotation (not currently supported).
     */
    ID_CLASS
}
